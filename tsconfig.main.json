{
  // Separate config for main-layer CJS build; do not extend app tsconfig to avoid noEmit settings

  "compilerOptions": {
    "module": "CommonJS",
    "target": "ES2020",
    "outDir": "build/main",
    "rootDir": "src/main",
    "noEmit": false,
    "moduleResolution": "Node",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "declaration": false,
    "sourceMap": false,
    "resolveJsonModule": true,
    "isolatedModules": false,
    "lib": ["ES2020", "DOM"],
    "types": []
  },
  "include": [
    "src/main/ipc/secure-ipc.ts",
    "src/main/ipc/schemas.ts",
    "src/main/handlers/state-handlers.ts",
    "src/main/db/database-manager.ts",
    "src/main/db/secure-database.ts",
    "src/main/db/async-database.ts",
    "src/main/db/retry-utils.ts",
    "src/main/db/shared-buffer-utils.ts",
    "src/main/utils/token-utils.ts"
  ],
  "exclude": [
    "src/main/db/__tests__/**",
    "src/main/db/index.ts",
    "src/main/db/connection-pool.ts",
    "src/main/db/pooled-*.ts",
    "src/main/db/pool-config.ts",
    "src/main/db/pooled-database-bridge.ts"
  ]
}

